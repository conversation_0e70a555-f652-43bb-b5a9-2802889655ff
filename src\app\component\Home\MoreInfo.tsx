// import React from 'react';

// const FeaturesSection = () => {
//   return (
//     <section className="w-full py-12 bg-white">
//       <div className="container mx-auto px-4 md:px-8">
//         <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
//           {/* Enhanced Durability & Strength */}
//           <div className="bg-white shadow-lg rounded-lg p-8 text-center flex flex-col items-center">
//             <div className="bg-gray-100 rounded-full p-3 w-16 h-16 flex items-center justify-center mb-6">
//               <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
//                 <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z" fill="#FFFFFF" stroke="#000000" strokeWidth="2" />
//               </svg>
//             </div>
//             <h3 className="text-gray-800 text-xl font-bold mb-4">Enhanced Durability & Strength</h3>
//             <p className="text-gray-600 text-sm">
//               Benefits of Comobe Silica Fume in concrete (resistance to sulfates, chlorides, etc.).
//             </p>
//           </div>

//           {/* Environmental Commitment */}
//           <div className="bg-white shadow-lg rounded-lg p-8 text-center flex flex-col items-center">
//             <div className="bg-gray-100 rounded-full p-3 w-16 h-16 flex items-center justify-center mb-6">
//               <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
//                 <circle cx="12" cy="12" r="10" fill="#000000" />
//                 <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z" fill="#FFFFFF" />
//               </svg>
//             </div>
//             <h3 className="text-gray-800 text-xl font-bold mb-4">Environmental Commitment</h3>
//             <p className="text-gray-600 text-sm">
//               Mention the environmentally friendly manufacturing process, minimizing pollution compared to traditional materials.
//             </p>
//           </div>

//           {/* ISO and Quality Assurance */}
//           <div className="bg-white shadow-lg rounded-lg p-8 text-center flex flex-col items-center">
//             <div className="bg-gray-100 rounded-full p-3 w-16 h-16 flex items-center justify-center mb-6">
//               <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
//                 <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" fill="#000000" />
//                 <circle cx="12" cy="12" r="3" fill="#FFFFFF" />
//               </svg>
//             </div>
//             <h3 className="text-gray-800 text-xl font-bold mb-4">ISO and Quality Assurance</h3>
//             <p className="text-gray-600 text-sm">
//               Highlight the ISO certification, registered trademark, and rigorous quality standards.
//             </p>
//           </div>
//         </div>
//       </div>
//     </section>
//   );
// };

// export default FeaturesSection;

import React from 'react'

const MoreInfo = () => {
  return (
    <div>
      
    </div>
  )
}

export default MoreInfo
