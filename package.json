{"name": "shiv<PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"dotenv": "^16.5.0", "framer-motion": "^12.0.6", "fs.promises": "^0.1.2", "lucide-react": "^0.474.0", "next": "15.1.6", "nodemailer": "^6.10.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.4.0", "react-markdown": "^10.1.0", "swiper": "^11.2.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.6", "postcss": "^8", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.1", "typescript": "^5"}}